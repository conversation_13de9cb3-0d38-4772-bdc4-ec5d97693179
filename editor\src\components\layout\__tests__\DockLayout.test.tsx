/**
 * DockLayout组件测试
 */
import { render, screen } from '../../../__tests__/utils/test-utils';
import DockLayout from '../DockLayout';
import { LayoutData } from 'rc-dock';
import { jest } from '@jest/globals';

// 模拟rc-dock库
jest.mock('rc-dock', () => {
  return {
    DockLayout: React.forwardRef(({ defaultLayout, onLayoutChange, style }: any, ref: any) => (
      <div data-testid="mock-dock-layout" style={style} ref={ref}>
        <div data-testid="mock-dock-layout-content">
          {JSON.stringify(defaultLayout)}
        </div>
        <button
          data-testid="mock-layout-change-button"
          onClick={() => onLayoutChange && onLayoutChange({ test: 'new-layout' })}
        >
          Change Layout
        </button>
      </div>
    ))
  };
});

describe('DockLayout组件', () => {
  const mockDefaultLayout: LayoutData = {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          tabs: [
            { id: 'test', title: '测试', content: 'test-content' }
          ]
        }
      ]
    }
  };

  const mockOnLayoutChange = jest.fn() as any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染DockLayout组件', () => {
    render(
      <DockLayout 
        defaultLayout={mockDefaultLayout} 
        onLayoutChange={mockOnLayoutChange} 
      />
    );

    expect(screen.getByTestId('mock-dock-layout')).toBeInTheDocument();
    expect(screen.getByTestId('mock-dock-layout-content')).toHaveTextContent(JSON.stringify(mockDefaultLayout));
  });

  it('应该在布局变化时调用onLayoutChange回调', () => {
    render(
      <DockLayout 
        defaultLayout={mockDefaultLayout} 
        onLayoutChange={mockOnLayoutChange} 
      />
    );

    const changeButton = screen.getByTestId('mock-layout-change-button');
    changeButton.click();

    expect(mockOnLayoutChange).toHaveBeenCalledTimes(1);
    expect(mockOnLayoutChange).toHaveBeenCalledWith({ test: 'new-layout' });
  });

  it('应该应用正确的主题类名', () => {
    const { container } = render(
      <DockLayout 
        defaultLayout={mockDefaultLayout} 
        onLayoutChange={mockOnLayoutChange} 
      />
    );

    // 默认应该使用亮色主题
    expect(container.firstChild).toHaveClass('dock-layout-container');
    
    // 注意：由于我们模拟了rc-dock，实际上不会渲染真正的类名，
    // 这里只是一个示例，实际测试中需要根据实际情况调整
  });
});
