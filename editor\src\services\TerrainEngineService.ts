/**
 * 地形引擎集成服务
 * 负责编辑器与底层地形引擎的集成
 */
// 临时类型定义，替代引擎导入
interface TerrainComponent {
  width: number;
  height: number;
  resolution: number;
  maxHeight: number;
  useLOD: boolean;
  usePhysics: boolean;
  layers: any[];
  heightData: Float32Array;
  needsUpdate: boolean;
  needsPhysicsUpdate: boolean;
  importFromHeightMap(data: ArrayBuffer, options: any): Promise<boolean>;
}

interface TerrainSystem {
  // 地形系统接口
}

// 地形组件构造函数
class TerrainComponentImpl implements TerrainComponent {
  width: number;
  height: number;
  resolution: number;
  maxHeight: number;
  useLOD: boolean;
  usePhysics: boolean;
  layers: any[];
  heightData: Float32Array;
  needsUpdate: boolean = false;
  needsPhysicsUpdate: boolean = false;

  constructor(options: any) {
    this.width = options.width || 1000;
    this.height = options.height || 1000;
    this.resolution = options.resolution || 256;
    this.maxHeight = options.maxHeight || 100;
    this.useLOD = options.useLOD || false;
    this.usePhysics = options.usePhysics || false;
    this.layers = options.layers || [];
    this.heightData = new Float32Array(this.resolution * this.resolution);
  }

  async importFromHeightMap(data: ArrayBuffer, options: any): Promise<boolean> {
    // 模拟实现
    return true;
  }
}
import { store } from '../store';
import { updateTerrainComponent, addOperation, TerrainOperationType } from '../store/terrain/terrainSlice';
import { updateEntity } from '../store/scene/sceneSlice';

/**
 * 地形创建选项
 */
export interface TerrainCreationOptions {
  width?: number;
  height?: number;
  resolution?: number;
  maxHeight?: number;
  useLOD?: boolean;
  usePhysics?: boolean;
  layers?: any[];
}

/**
 * 地形雕刻选项
 */
export interface TerrainSculptingOptions {
  brushType: string;
  brushShape: string;
  brushSize: number;
  brushStrength: number;
  brushFalloff: number;
  targetHeight?: number;
  position: { x: number; z: number };
}

/**
 * 地形引擎集成服务
 */
export class TerrainEngineService {
  private static instance: TerrainEngineService;
  private terrainSystem: TerrainSystem | null = null;
  private terrainComponents: Map<string, TerrainComponent> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): TerrainEngineService {
    if (!TerrainEngineService.instance) {
      TerrainEngineService.instance = new TerrainEngineService();
    }
    return TerrainEngineService.instance;
  }

  /**
   * 初始化地形系统
   * @param terrainSystem 地形系统实例
   */
  public initialize(terrainSystem: TerrainSystem): void {
    this.terrainSystem = terrainSystem;
  }

  /**
   * 创建地形组件
   * @param entityId 实体ID
   * @param options 创建选项
   * @returns 创建的地形组件
   */
  public async createTerrainComponent(entityId: string, options: TerrainCreationOptions = {}): Promise<TerrainComponent> {
    try {
      // 创建地形组件
      const terrainComponent = new TerrainComponentImpl({
        width: options.width || 1000,
        height: options.height || 1000,
        resolution: options.resolution || 256,
        maxHeight: options.maxHeight || 100,
        useLOD: options.useLOD || false,
        usePhysics: options.usePhysics || false,
        layers: options.layers || []
      });

      // 存储地形组件
      this.terrainComponents.set(entityId, terrainComponent);

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 更新实体组件
      store.dispatch(updateEntity({
        id: entityId,
        components: {
          TerrainComponent: this.serializeTerrainComponent(terrainComponent)
        }
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.CREATE,
        entityId,
        data: options,
        description: '创建地形组件'
      }));

      return terrainComponent;
    } catch (error) {
      console.error('创建地形组件失败:', error);
      throw error;
    }
  }

  /**
   * 获取地形组件
   * @param entityId 实体ID
   * @returns 地形组件
   */
  public getTerrainComponent(entityId: string): TerrainComponent | null {
    return this.terrainComponents.get(entityId) || null;
  }

  /**
   * 雕刻地形
   * @param entityId 实体ID
   * @param options 雕刻选项
   */
  public async sculptTerrain(entityId: string, options: TerrainSculptingOptions): Promise<void> {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 执行雕刻操作
      await this.performSculptingOperation(terrainComponent, options);

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.SCULPT,
        entityId,
        data: options,
        description: `地形雕刻: ${options.brushType}`
      }));
    } catch (error) {
      console.error('地形雕刻失败:', error);
      throw error;
    }
  }

  /**
   * 生成地形
   * @param entityId 实体ID
   * @param algorithm 生成算法
   * @param params 算法参数
   */
  public async generateTerrain(entityId: string, algorithm: string, params: any): Promise<void> {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 调用地形生成算法
      await this.executeGenerationAlgorithm(terrainComponent, algorithm, params);

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.GENERATE,
        entityId,
        data: { algorithm, params },
        description: `生成地形: ${algorithm}`
      }));
    } catch (error) {
      console.error('地形生成失败:', error);
      throw error;
    }
  }

  /**
   * 导入高度图
   * @param entityId 实体ID
   * @param file 高度图文件
   * @param options 导入选项
   */
  public async importHeightMap(entityId: string, file: File, options: any = {}): Promise<void> {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 读取文件
      const arrayBuffer = await file.arrayBuffer();
      
      // 导入高度图
      const success = await terrainComponent.importFromHeightMap(arrayBuffer, {
        format: this.getHeightMapFormat(file.name),
        computeNormals: true,
        applySmoothing: options.applySmoothing || false,
        ...options
      });

      if (!success) {
        throw new Error('高度图导入失败');
      }

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.IMPORT,
        entityId,
        data: { fileName: file.name, options },
        description: `导入高度图: ${file.name}`
      }));
    } catch (error) {
      console.error('导入高度图失败:', error);
      throw error;
    }
  }

  /**
   * 更新地形物理设置
   * @param entityId 实体ID
   * @param physicsSettings 物理设置
   */
  public updateTerrainPhysics(entityId: string, physicsSettings: any): void {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 更新物理设置
      Object.assign(terrainComponent, physicsSettings);
      terrainComponent.needsPhysicsUpdate = true;

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.PHYSICS,
        entityId,
        data: physicsSettings,
        description: '更新地形物理设置'
      }));
    } catch (error) {
      console.error('更新地形物理设置失败:', error);
      throw error;
    }
  }

  /**
   * 执行雕刻操作
   * @param terrainComponent 地形组件
   * @param options 雕刻选项
   */
  private async performSculptingOperation(terrainComponent: TerrainComponent, options: TerrainSculptingOptions): Promise<void> {
    const { brushType, brushSize, brushStrength, position } = options;

    // 计算影响区域
    const radius = brushSize / 2;
    const centerX = Math.floor((position.x + terrainComponent.width / 2) / terrainComponent.width * terrainComponent.resolution);
    const centerZ = Math.floor((position.z + terrainComponent.height / 2) / terrainComponent.height * terrainComponent.resolution);

    // 应用笔刷效果
    for (let z = Math.max(0, centerZ - radius); z <= Math.min(terrainComponent.resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(terrainComponent.resolution - 1, centerX + radius); x++) {
        const dx = x - centerX;
        const dz = z - centerZ;
        const distance = Math.sqrt(dx * dx + dz * dz);

        if (distance <= radius) {
          const index = z * terrainComponent.resolution + x;
          const falloff = Math.pow(1 - distance / radius, options.brushFalloff);
          const delta = brushStrength * falloff;

          switch (brushType) {
            case 'raise':
              terrainComponent.heightData[index] += delta;
              break;
            case 'lower':
              terrainComponent.heightData[index] -= delta;
              break;
            case 'flatten':
              const targetHeight = options.targetHeight || 0.5;
              terrainComponent.heightData[index] = terrainComponent.heightData[index] * (1 - delta) + targetHeight * delta;
              break;
            case 'smooth':
              // 简化的平滑算法
              let sum = 0;
              let count = 0;
              for (let oz = -1; oz <= 1; oz++) {
                for (let ox = -1; ox <= 1; ox++) {
                  const nx = x + ox;
                  const nz = z + oz;
                  if (nx >= 0 && nx < terrainComponent.resolution && nz >= 0 && nz < terrainComponent.resolution) {
                    sum += terrainComponent.heightData[nz * terrainComponent.resolution + nx];
                    count++;
                  }
                }
              }
              const average = sum / count;
              terrainComponent.heightData[index] = terrainComponent.heightData[index] * (1 - delta) + average * delta;
              break;
          }

          // 确保高度在有效范围内
          terrainComponent.heightData[index] = Math.max(0, Math.min(1, terrainComponent.heightData[index]));
        }
      }
    }

    // 标记需要更新
    terrainComponent.needsUpdate = true;
  }

  /**
   * 序列化地形组件
   * @param terrainComponent 地形组件
   * @returns 序列化后的数据
   */
  private serializeTerrainComponent(terrainComponent: TerrainComponent): any {
    return {
      width: terrainComponent.width,
      height: terrainComponent.height,
      resolution: terrainComponent.resolution,
      maxHeight: terrainComponent.maxHeight,
      useLOD: terrainComponent.useLOD,
      usePhysics: terrainComponent.usePhysics,
      layers: terrainComponent.layers,
      needsUpdate: terrainComponent.needsUpdate,
      needsPhysicsUpdate: terrainComponent.needsPhysicsUpdate,
      // 注意：heightData 很大，可能需要特殊处理
      heightDataChecksum: this.calculateChecksum(terrainComponent.heightData)
    };
  }

  /**
   * 获取高度图格式
   * @param fileName 文件名
   * @returns 格式
   */
  private getHeightMapFormat(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'png':
        return 'png';
      case 'jpg':
      case 'jpeg':
        return 'jpeg';
      case 'raw':
        return 'raw';
      case 'r16':
        return 'r16';
      case 'r32':
        return 'r32';
      default:
        return 'png';
    }
  }

  /**
   * 执行地形生成算法
   * @param terrainComponent 地形组件
   * @param algorithm 算法名称
   * @param params 参数
   */
  private async executeGenerationAlgorithm(terrainComponent: TerrainComponent, algorithm: string, params: any): Promise<void> {
    const { resolution } = terrainComponent;

    switch (algorithm) {
      case 'perlin':
        this.generatePerlinNoise(terrainComponent, params);
        break;
      case 'fractal':
        this.generateFractalNoise(terrainComponent, params);
        break;
      case 'hills':
        this.generateHills(terrainComponent, params);
        break;
      case 'mountains':
        this.generateMountains(terrainComponent, params);
        break;
      case 'plains':
        this.generatePlains(terrainComponent, params);
        break;
      default:
        // 默认使用柏林噪声
        this.generatePerlinNoise(terrainComponent, params);
        break;
    }

    // 标记需要更新
    terrainComponent.needsUpdate = true;
    terrainComponent.needsPhysicsUpdate = true;
  }

  /**
   * 生成柏林噪声地形
   * @param terrainComponent 地形组件
   * @param params 参数
   */
  private generatePerlinNoise(terrainComponent: TerrainComponent, params: any): void {
    const { resolution } = terrainComponent;
    const { seed = 0, scale = 100, persistence = 0.5, octaves = 6, frequency = 0.01, amplitude = 1.0 } = params;

    // 简化的柏林噪声实现
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        let height = 0;
        let amp = amplitude;
        let freq = frequency;

        for (let i = 0; i < octaves; i++) {
          const noiseValue = this.noise((x + seed) * freq, (z + seed) * freq);
          height += noiseValue * amp;
          amp *= persistence;
          freq *= 2;
        }

        // 归一化到 [0, 1]
        height = (height + 1) * 0.5;
        height = Math.max(0, Math.min(1, height));

        const index = z * resolution + x;
        terrainComponent.heightData[index] = height;
      }
    }
  }

  /**
   * 生成分形噪声地形
   * @param terrainComponent 地形组件
   * @param params 参数
   */
  private generateFractalNoise(terrainComponent: TerrainComponent, params: any): void {
    const { resolution } = terrainComponent;
    const { seed = 0, scale = 150, persistence = 0.65, octaves = 8, frequency = 0.01, amplitude = 1.2 } = params;

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        let height = 0;
        let amp = amplitude;
        let freq = frequency;

        for (let i = 0; i < octaves; i++) {
          const noiseValue = this.noise((x + seed) * freq, (z + seed) * freq);
          height += Math.abs(noiseValue) * amp; // 使用绝对值创建更尖锐的特征
          amp *= persistence;
          freq *= 2;
        }

        height = Math.max(0, Math.min(1, height));

        const index = z * resolution + x;
        terrainComponent.heightData[index] = height;
      }
    }
  }

  /**
   * 生成丘陵地形
   * @param terrainComponent 地形组件
   * @param params 参数
   */
  private generateHills(terrainComponent: TerrainComponent, params: any): void {
    const { resolution } = terrainComponent;
    const { seed = 0, scale = 80, persistence = 0.4, octaves = 5, frequency = 0.008, amplitude = 0.8 } = params;

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        let height = 0;
        let amp = amplitude;
        let freq = frequency;

        for (let i = 0; i < octaves; i++) {
          const noiseValue = this.noise((x + seed) * freq, (z + seed) * freq);
          height += noiseValue * amp;
          amp *= persistence;
          freq *= 2;
        }

        // 应用平滑曲线使丘陵更圆润
        height = (height + 1) * 0.5;
        height = height * height * (3 - 2 * height); // 平滑步函数
        height = Math.max(0, Math.min(1, height));

        const index = z * resolution + x;
        terrainComponent.heightData[index] = height;
      }
    }
  }

  /**
   * 生成山脉地形
   * @param terrainComponent 地形组件
   * @param params 参数
   */
  private generateMountains(terrainComponent: TerrainComponent, params: any): void {
    const { resolution } = terrainComponent;
    const { seed = 0, scale = 150, persistence = 0.65, octaves = 8, frequency = 0.01, amplitude = 1.2 } = params;

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        let height = 0;
        let amp = amplitude;
        let freq = frequency;

        for (let i = 0; i < octaves; i++) {
          const noiseValue = this.noise((x + seed) * freq, (z + seed) * freq);
          // 使用脊状噪声创建山脊效果
          const ridgeNoise = 1 - Math.abs(noiseValue);
          height += ridgeNoise * ridgeNoise * amp;
          amp *= persistence;
          freq *= 2;
        }

        height = Math.max(0, Math.min(1, height));

        const index = z * resolution + x;
        terrainComponent.heightData[index] = height;
      }
    }
  }

  /**
   * 生成平原地形
   * @param terrainComponent 地形组件
   * @param params 参数
   */
  private generatePlains(terrainComponent: TerrainComponent, params: any): void {
    const { resolution } = terrainComponent;
    const { baseHeight = 0.1, variation = 0.05, seed = 0 } = params;

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        // 添加轻微的变化
        const noiseValue = this.noise((x + seed) * 0.01, (z + seed) * 0.01);
        const height = baseHeight + noiseValue * variation;

        const index = z * resolution + x;
        terrainComponent.heightData[index] = Math.max(0, Math.min(1, height));
      }
    }
  }

  /**
   * 简化的噪声函数
   * @param x X坐标
   * @param y Y坐标
   * @returns 噪声值 [-1, 1]
   */
  private noise(x: number, y: number): number {
    // 简化的伪随机噪声实现
    const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
    return 2 * (n - Math.floor(n)) - 1;
  }

  /**
   * 计算数据校验和
   * @param data 数据
   * @returns 校验和
   */
  private calculateChecksum(data: Float32Array): string {
    let sum = 0;
    for (let i = 0; i < data.length; i += 100) { // 采样计算以提高性能
      sum += data[i];
    }
    return sum.toString(36);
  }
}

// 导出单例实例
export const terrainEngineService = TerrainEngineService.getInstance();
