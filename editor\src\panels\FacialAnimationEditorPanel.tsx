/**
 * 面部动画编辑器面板
 * 提供直观的面部动画编辑功能
 */
import React, { useState } from 'react';
import { Tabs, Select, Slider, Switch, message } from 'antd';
import { VisemeEditor } from '../components/VisemeEditor';
import { FacialAnimationPresetPanel } from './FacialAnimationPresetPanel';
import { useTranslation } from 'react-i18next';
// 导入类型定义
import { FacialExpressionType, VisemeType } from '../components/animation/FacialAnimationEditor';
import { FacialAnimationPresetType } from '../components/FacialAnimationEditor/FacialAnimationPresetManager';
import './FacialAnimationEditorPanel.less';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 面部动画编辑器面板属性
 */
interface FacialAnimationEditorPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 是否显示3D预览 */
  showPreview?: boolean;
  /** 是否显示时间轴 */
  showTimeline?: boolean;
  /** 是否显示预设列表 */
  showPresets?: boolean;
  /** 是否显示肌肉编辑器 */
  showMuscleEditor?: boolean;
  /** 是否显示曲线编辑器 */
  showCurveEditor?: boolean;
  /** 是否显示口型编辑器 */
  showVisemeEditor?: boolean;
  /** 是否显示BlendShape编辑器 */
  showBlendShapeEditor?: boolean;
  /** 面板宽度 */
  width?: number | string;
  /** 面板高度 */
  height?: number | string;
  /** 默认激活的标签页 */
  defaultActiveTab?: string;
  /** 默认表情 */
  defaultExpression?: FacialExpressionType;
  /** 默认表情权重 */
  defaultExpressionWeight?: number;
  /** 默认口型 */
  defaultViseme?: VisemeType;
  /** 默认口型权重 */
  defaultVisemeWeight?: number;
  /** 默认预设类型 */
  defaultPresetType?: FacialAnimationPresetType;
  /** 默认文化 */
  defaultCulture?: string;
  /** 表情变更回调 */
  onExpressionChange?: (expression: FacialExpressionType, weight: number) => void;
  /** 口型变更回调 */
  onVisemeChange?: (viseme: VisemeType, weight: number) => void;
  /** 预设应用回调 */
  onPresetApply?: (presetName: string) => void;
  /** 保存回调 */
  onSave?: () => void;
}

/**
 * 面部动画编辑器面板
 */
export const FacialAnimationEditorPanel: React.FC<FacialAnimationEditorPanelProps> = ({
  entityId,
  editable = true,
  showPresets = true,
  showVisemeEditor = true,
  showBlendShapeEditor = true,
  width = '100%',
  height = '100%',
  defaultActiveTab = 'expressions',
  defaultExpression = FacialExpressionType.NEUTRAL,
  defaultExpressionWeight = 1.0,
  defaultViseme = VisemeType.NEUTRAL,
  defaultVisemeWeight = 1.0,
  defaultPresetType = FacialAnimationPresetType.STANDARD,
  defaultCulture = 'global',
  onExpressionChange,
  onVisemeChange,
  onPresetApply
}) => {
  const { t } = useTranslation();

  // 状态
  const [activeTab, setActiveTab] = useState<string>(defaultActiveTab);
  const [currentExpression, setCurrentExpression] = useState<FacialExpressionType>(defaultExpression);
  const [currentExpressionWeight, setCurrentExpressionWeight] = useState<number>(defaultExpressionWeight);
  const [currentViseme, setCurrentViseme] = useState<VisemeType>(defaultViseme);
  const [currentVisemeWeight, setCurrentVisemeWeight] = useState<number>(defaultVisemeWeight);
  const [currentPresetType] = useState<FacialAnimationPresetType>(defaultPresetType);
  const [currentCulture] = useState<string>(defaultCulture);
  const [useAudioSync, setUseAudioSync] = useState<boolean>(false);

  // 表情列表
  const expressions = [
    { value: FacialExpressionType.NEUTRAL, label: '中性' },
    { value: FacialExpressionType.HAPPY, label: '开心' },
    { value: FacialExpressionType.SAD, label: '悲伤' },
    { value: FacialExpressionType.ANGRY, label: '愤怒' },
    { value: FacialExpressionType.SURPRISED, label: '惊讶' },
    { value: FacialExpressionType.FEAR, label: '恐惧' },
    { value: FacialExpressionType.DISGUST, label: '厌恶' },
    { value: FacialExpressionType.CONTEMPT, label: '蔑视' }
  ];

  // 口型列表
  const visemes = [
    { value: VisemeType.NEUTRAL, label: '中性' },
    { value: VisemeType.AA, label: 'AA' },
    { value: VisemeType.CH, label: 'CH' },
    { value: VisemeType.DD, label: 'DD' },
    { value: VisemeType.E, label: 'E' },
    { value: VisemeType.FF, label: 'FF' },
    { value: VisemeType.IH, label: 'IH' },
    { value: VisemeType.K, label: 'K' },
    { value: VisemeType.NN, label: 'NN' },
    { value: VisemeType.OH, label: 'OH' },
    { value: VisemeType.OU, label: 'OU' },
    { value: VisemeType.PP, label: 'PP' },
    { value: VisemeType.R, label: 'R' },
    { value: VisemeType.SS, label: 'SS' },
    { value: VisemeType.TH, label: 'TH' }
  ];



  // 处理表情变更
  const handleExpressionChange = (expression: FacialExpressionType) => {
    setCurrentExpression(expression);
    if (onExpressionChange) {
      onExpressionChange(expression, currentExpressionWeight);
    }
  };

  // 处理表情权重变更
  const handleExpressionWeightChange = (weight: number) => {
    setCurrentExpressionWeight(weight);
    if (onExpressionChange) {
      onExpressionChange(currentExpression, weight);
    }
  };

  // 处理口型变更
  const handleVisemeChange = (viseme: VisemeType) => {
    setCurrentViseme(viseme);
    if (onVisemeChange) {
      onVisemeChange(viseme, currentVisemeWeight);
    }
  };

  // 处理口型权重变更
  const handleVisemeWeightChange = (weight: number) => {
    setCurrentVisemeWeight(weight);
    if (onVisemeChange) {
      onVisemeChange(currentViseme, weight);
    }
  };

  // 处理预设应用
  const handlePresetApply = (presetName: string) => {
    if (onPresetApply) {
      onPresetApply(presetName);
    }
    message.success(`已应用预设: ${presetName}`);
  };



  // 处理音频同步开关变更
  const handleAudioSyncToggle = (checked: boolean) => {
    setUseAudioSync(checked);
  };

  return (
    <div className="facial-animation-editor-panel" style={{ width, height }}>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="表情" key="expressions">
          <div className="expressions-tab">
            <div className="expression-controls">
              <div className="control-group">
                <label>表情:</label>
                <Select
                  value={currentExpression}
                  onChange={handleExpressionChange}
                  disabled={!editable}
                  style={{ width: 120 }}
                >
                  {expressions.map(expr => (
                    <Option key={expr.value} value={expr.value}>{expr.label}</Option>
                  ))}
                </Select>
              </div>

              <div className="control-group">
                <label>权重:</label>
                <Slider
                  value={currentExpressionWeight}
                  onChange={handleExpressionWeightChange}
                  min={0}
                  max={1}
                  step={0.01}
                  disabled={!editable}
                  style={{ width: 200 }}
                />
              </div>
            </div>

            {showBlendShapeEditor && (
              <div className="blend-shape-editor-container">
                <h3>BlendShape 编辑器</h3>
                <div className="blend-shape-placeholder">
                  <p>BlendShape 编辑器功能正在开发中...</p>
                  <p>当前表情: {currentExpression}</p>
                  <p>权重: {currentExpressionWeight}</p>
                </div>
              </div>
            )}
          </div>
        </TabPane>

        <TabPane tab="口型" key="visemes">
          <div className="visemes-tab">
            <div className="viseme-controls">
              <div className="control-group">
                <label>口型:</label>
                <Select
                  value={currentViseme}
                  onChange={handleVisemeChange}
                  disabled={!editable}
                  style={{ width: 120 }}
                >
                  {visemes.map(vis => (
                    <Option key={vis.value} value={vis.value}>{vis.label}</Option>
                  ))}
                </Select>
              </div>

              <div className="control-group">
                <label>权重:</label>
                <Slider
                  value={currentVisemeWeight}
                  onChange={handleVisemeWeightChange}
                  min={0}
                  max={1}
                  step={0.01}
                  disabled={!editable}
                  style={{ width: 200 }}
                />
              </div>

              <div className="control-group">
                <Switch
                  checked={useAudioSync}
                  onChange={handleAudioSyncToggle}
                  disabled={!editable}
                />
                <label>音频同步</label>
              </div>
            </div>

            {showVisemeEditor && (
              <div className="viseme-editor-container">
                <h3>口型编辑器</h3>
                <VisemeEditor
                  entityId={entityId}
                  viseme={currentViseme as any}
                  weight={currentVisemeWeight}
                  editable={editable}
                />
              </div>
            )}
          </div>
        </TabPane>

        <TabPane tab={t('editor.animation.presets')} key="presets">
          <div className="presets-tab">
            {showPresets && (
              <FacialAnimationPresetPanel
                entityId={entityId}
                editable={editable}
                defaultActiveTab="presets"
                defaultPresetType={currentPresetType}
                defaultCulture={currentCulture}
                onPresetApply={handlePresetApply}
                onTemplateApply={(template, _parameters) => {
                  // 这里应该处理模板应用
                  message.success(`已应用模板: ${template.name}`);
                }}
              />
            )}
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};
