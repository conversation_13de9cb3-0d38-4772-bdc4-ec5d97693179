/**
 * 增强用户会话管理器测试
 */
import { 
  EnhancedUserSessionManager, 
  UserRole, 
  UserSessionState, 
  UserActivityType 
} from '../EnhancedUserSessionManager';

// Mock 依赖
jest.mock('../PermissionLogService', () => ({
  permissionLogService: {
    logUserSessionCreated: jest.fn(),
    logUserSessionRemoved: jest.fn()
  }
}));

jest.mock('../UserInteractionLogService', () => ({
  userInteractionLogService: {
    logUserActivity: jest.fn()
  },
  UserActivityType: {
    EDIT: 'edit',
    VIEW: 'view',
    CREATE: 'create',
    DELETE: 'delete',
    NAVIGATE: 'navigate',
    TOOL_USE: 'tool_use',
    COLLABORATION: 'collaboration'
  }
}));

describe('EnhancedUserSessionManager', () => {
  let sessionManager: EnhancedUserSessionManager;

  beforeEach(() => {
    // 创建新的会话管理器实例
    sessionManager = new EnhancedUserSessionManager({
      sessionTimeout: 1000, // 1秒用于测试
      idleTimeout: 500, // 0.5秒用于测试
      enableSessionTimeout: true,
      enablePermissionCheck: true,
      defaultRole: UserRole.USER,
      allowAnonymous: true,
      maxUsers: 10,
      recordActivityHistory: true,
      maxActivityHistory: 5,
      enableAutoStateUpdate: true,
      enableSessionRecovery: true,
      enableSessionSync: true
    });
  });

  afterEach(() => {
    // 清理
    sessionManager.destroy();
  });

  describe('会话创建', () => {
    it('应该成功创建新会话', () => {
      const session = sessionManager.createSession(
        'user1',
        'testuser',
        UserRole.USER,
        true,
        'token123',
        {
          ip: '127.0.0.1',
          userAgent: 'test-agent',
          deviceType: 'desktop'
        }
      );

      expect(session).toBeDefined();
      expect(session.userId).toBe('user1');
      expect(session.username).toBe('testuser');
      expect(session.role).toBe(UserRole.USER);
      expect(session.isOnline).toBe(true);
      expect(session.isAuthenticated).toBe(true);
      expect(session.state).toBe(UserSessionState.ACTIVE);
      expect(session.sessionToken).toBe('token123');
    });

    it('应该更新现有会话而不是创建新会话', () => {
      // 创建第一个会话
      const session1 = sessionManager.createSession('user1', 'testuser');
      const originalCreatedAt = session1.createdAt;

      // 尝试创建相同用户的会话
      const session2 = sessionManager.createSession('user1', 'testuser');

      expect(session2.userId).toBe('user1');
      expect(session2.createdAt).toBe(originalCreatedAt); // 应该是同一个会话
      expect(sessionManager.getAllSessions()).toHaveLength(1);
    });

    it('应该在达到最大用户数时抛出错误', () => {
      // 创建最大数量的用户
      for (let i = 0; i < 10; i++) {
        sessionManager.createSession(`user${i}`, `testuser${i}`);
      }

      // 尝试创建第11个用户
      expect(() => {
        sessionManager.createSession('user10', 'testuser10');
      }).toThrow('Maximum number of users (10) reached');
    });

    it('应该在不允许匿名用户时拒绝未认证用户', () => {
      const restrictiveManager = new EnhancedUserSessionManager({
        allowAnonymous: false
      });

      expect(() => {
        restrictiveManager.createSession('user1', 'testuser', UserRole.USER, false);
      }).toThrow('Anonymous users are not allowed');

      restrictiveManager.destroy();
    });
  });

  describe('用户活动记录', () => {
    it('应该记录用户活动', () => {
      const session = sessionManager.createSession('user1', 'testuser');
      
      sessionManager.recordUserActivity(
        'user1',
        UserActivityType.EDIT,
        { action: 'edit_entity' },
        'entity123',
        'entity'
      );

      expect(session.activityHistory).toHaveLength(2); // 创建会话 + 编辑活动
      expect(session.lastActivityType).toBe(UserActivityType.EDIT);
    });

    it('应该限制活动历史大小', () => {
      const session = sessionManager.createSession('user1', 'testuser');
      
      // 记录超过最大历史数量的活动
      for (let i = 0; i < 10; i++) {
        sessionManager.recordUserActivity('user1', UserActivityType.VIEW);
      }

      expect(session.activityHistory!.length).toBeLessThanOrEqual(5);
    });

    it('应该在有新活动时将空闲用户状态更新为活跃', () => {
      const session = sessionManager.createSession('user1', 'testuser');
      
      // 手动设置为空闲状态
      sessionManager.setUserState('user1', UserSessionState.IDLE);
      expect(session.state).toBe(UserSessionState.IDLE);

      // 记录新活动
      sessionManager.recordUserActivity('user1', UserActivityType.EDIT);
      
      expect(session.state).toBe(UserSessionState.ACTIVE);
    });
  });

  describe('用户状态管理', () => {
    it('应该成功设置用户状态', () => {
      sessionManager.createSession('user1', 'testuser');
      
      const result = sessionManager.setUserState('user1', UserSessionState.BUSY);
      
      expect(result).toBe(true);
      const session = sessionManager.getSession('user1');
      expect(session!.state).toBe(UserSessionState.BUSY);
    });

    it('应该在设置离线状态时更新在线状态', () => {
      sessionManager.createSession('user1', 'testuser');
      
      sessionManager.setUserState('user1', UserSessionState.OFFLINE);
      
      const session = sessionManager.getSession('user1');
      expect(session!.state).toBe(UserSessionState.OFFLINE);
      expect(session!.isOnline).toBe(false);
    });

    it('应该在设置非离线状态时恢复在线状态', () => {
      const session = sessionManager.createSession('user1', 'testuser');
      
      // 先设置为离线
      session.isOnline = false;
      sessionManager.setUserState('user1', UserSessionState.ACTIVE);
      
      expect(session.isOnline).toBe(true);
    });
  });

  describe('会话管理', () => {
    it('应该成功移除会话', () => {
      sessionManager.createSession('user1', 'testuser');
      expect(sessionManager.getAllSessions()).toHaveLength(1);
      
      const result = sessionManager.removeSession('user1');
      
      expect(result).toBe(true);
      expect(sessionManager.getAllSessions()).toHaveLength(0);
    });

    it('应该返回所有会话', () => {
      sessionManager.createSession('user1', 'testuser1');
      sessionManager.createSession('user2', 'testuser2');
      
      const sessions = sessionManager.getAllSessions();
      
      expect(sessions).toHaveLength(2);
      expect(sessions.map(s => s.userId)).toContain('user1');
      expect(sessions.map(s => s.userId)).toContain('user2');
    });

    it('应该根据用户ID获取会话', () => {
      sessionManager.createSession('user1', 'testuser');
      
      const session = sessionManager.getSession('user1');
      
      expect(session).toBeDefined();
      expect(session!.userId).toBe('user1');
    });

    it('应该在获取不存在的会话时返回undefined', () => {
      const session = sessionManager.getSession('nonexistent');
      
      expect(session).toBeUndefined();
    });
  });

  describe('事件发射', () => {
    it('应该在创建会话时发射userCreated事件', (done) => {
      sessionManager.on('userCreated', (userId, session) => {
        expect(userId).toBe('user1');
        expect(session.userId).toBe('user1');
        done();
      });

      sessionManager.createSession('user1', 'testuser');
    });

    it('应该在状态变更时发射userStateChanged事件', (done) => {
      sessionManager.createSession('user1', 'testuser');
      
      sessionManager.on('userStateChanged', (userId, session) => {
        expect(userId).toBe('user1');
        expect(session.state).toBe(UserSessionState.BUSY);
        done();
      });

      sessionManager.setUserState('user1', UserSessionState.BUSY);
    });

    it('应该在记录活动时发射userActivity事件', (done) => {
      sessionManager.createSession('user1', 'testuser');
      
      let eventCount = 0;
      sessionManager.on('userActivity', (userId, activity) => {
        eventCount++;
        if (eventCount === 2) { // 跳过会话创建的活动
          expect(userId).toBe('user1');
          expect(activity.type).toBe(UserActivityType.EDIT);
          done();
        }
      });

      sessionManager.recordUserActivity('user1', UserActivityType.EDIT);
    });
  });

  describe('销毁', () => {
    it('应该正确清理资源', () => {
      sessionManager.createSession('user1', 'testuser');
      expect(sessionManager.getAllSessions()).toHaveLength(1);
      
      sessionManager.destroy();
      
      expect(sessionManager.getAllSessions()).toHaveLength(0);
      expect(sessionManager.listenerCount()).toBe(0);
    });
  });
});
